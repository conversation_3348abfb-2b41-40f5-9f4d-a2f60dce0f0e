[{"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_comments\\` has a row level security policy \\`Allow creating comments\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_comments_Allow creating comments"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_comments\\` has a row level security policy \\`Allow deleting own comments\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_comments_Allow deleting own comments"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_comments\\` has a row level security policy \\`Allow reading comments\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_comments_Allow reading comments"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_comments\\` has a row level security policy \\`Allow updating own comments\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_comments_Allow updating own comments"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_comment_likes\\` has a row level security policy \\`Allow all comment like operations\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_comment_likes", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_comment_likes_Allow all comment like operations"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_categories\\` has a row level security policy \\`Allow authenticated users to manage categories\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_categories", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_categories_Allow authenticated users to manage categories"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_shares\\` has a row level security policy \\`Allow reading post shares\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_shares", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_shares_Allow reading post shares"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_shares\\` has a row level security policy \\`Allow users to delete shares\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_shares", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_shares_Allow users to delete shares"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_shares\\` has a row level security policy \\`Allow users to share posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_shares", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_shares_Allow users to share posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.communities\\` has a row level security policy \\`Allow authenticated users to create communities\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "communities", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_communities_Allow authenticated users to create communities"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.communities\\` has a row level security policy \\`Allow creators and admins to delete communities\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "communities", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_communities_Allow creators and admins to delete communities"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.communities\\` has a row level security policy \\`Allow creators and admins to update communities\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "communities", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_communities_Allow creators and admins to update communities"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.communities\\` has a row level security policy \\`Allow public read access to communities\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "communities", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_communities_Allow public read access to communities"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_members\\` has a row level security policy \\`Allow admins and moderators to update member roles\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_members_Allow admins and moderators to update member roles"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_members\\` has a row level security policy \\`Allow joining communities\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_members_Allow joining communities"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_members\\` has a row level security policy \\`Allow leaving own membership\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_members_Allow leaving own membership"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_members\\` has a row level security policy \\`Allow reading memberships\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_members_Allow reading memberships"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_members\\` has a row level security policy \\`Allow updating own membership\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_members_Allow updating own membership"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_posts\\` has a row level security policy \\`Allow creating posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_posts", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_posts_Allow creating posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_posts\\` has a row level security policy \\`Allow deleting own posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_posts", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_posts_Allow deleting own posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_posts\\` has a row level security policy \\`Allow reading posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_posts", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_posts_Allow reading posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_posts\\` has a row level security policy \\`Allow updating own posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_posts", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_posts_Allow updating own posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`System can delete optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_System can delete optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`System can insert optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_System can insert optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`System can update optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_System can update optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.community_post_likes\\` has a row level security policy \\`Allow all like operations\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "community_post_likes", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_community_post_likes_Allow all like operations"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_categories\\` has multiple permissive policies for role \\`anon\\` for action \\`SELECT\\`. Policies include \\`{\"Allow authenticated users to manage categories\",\"Allow public read access to categories\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_categories", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_categories_anon_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_categories\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{\"Allow authenticated users to manage categories\",\"Allow public read access to categories\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_categories", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_categories_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_categories\\` has multiple permissive policies for role \\`authenticator\\` for action \\`SELECT\\`. Policies include \\`{\"Allow authenticated users to manage categories\",\"Allow public read access to categories\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_categories", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_categories_authenticator_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_categories\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`SELECT\\`. Policies include \\`{\"Allow authenticated users to manage categories\",\"Allow public read access to categories\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_categories", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_categories_dashboard_user_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_members\\` has multiple permissive policies for role \\`anon\\` for action \\`UPDATE\\`. Policies include \\`{\"Allow admins and moderators to update member roles\",\"Allow updating own membership\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_members_anon_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_members\\` has multiple permissive policies for role \\`authenticated\\` for action \\`UPDATE\\`. Policies include \\`{\"Allow admins and moderators to update member roles\",\"Allow updating own membership\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_members_authenticated_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_members\\` has multiple permissive policies for role \\`authenticator\\` for action \\`UPDATE\\`. Policies include \\`{\"Allow admins and moderators to update member roles\",\"Allow updating own membership\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_members_authenticator_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.community_members\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`UPDATE\\`. Policies include \\`{\"Allow admins and moderators to update member roles\",\"Allow updating own membership\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_community_members_dashboard_user_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`anon\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,room_participants_unified_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_anon_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`anon\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,room_participants_unified_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_anon_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticated\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,room_participants_unified_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticated_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,room_participants_unified_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticator\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,room_participants_unified_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticator_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticator\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,room_participants_unified_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticator_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,room_participants_unified_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_dashboard_user_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,room_participants_unified_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_dashboard_user_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`anon\\` for action \\`DELETE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_anon_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`anon\\` for action \\`INSERT\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_anon_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`anon\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_anon_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`anon\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_anon_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticated\\` for action \\`DELETE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticated_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticated\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticated_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticator\\` for action \\`DELETE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticator_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticator\\` for action \\`INSERT\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticator_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticator\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticator_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticator\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticator_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`DELETE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_dashboard_user_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`INSERT\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_dashboard_user_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_dashboard_user_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can manage their typing status\",typing_status_unified_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_dashboard_user_UPDATE"}]