[{"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.community_members\\` has a foreign key \\`community_members_invited_by_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "community_members", "type": "table", "schema": "public", "fkey_name": "community_members_invited_by_fkey", "fkey_columns": [7]}, "cache_key": "unindexed_foreign_keys_public_community_members_community_members_invited_by_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.public_key_audit\\` has a foreign key \\`public_key_audit_user_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "public_key_audit", "type": "table", "schema": "public", "fkey_name": "public_key_audit_user_id_fkey", "fkey_columns": [2]}, "cache_key": "unindexed_foreign_keys_public_public_key_audit_public_key_audit_user_id_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.reel_likes\\` has a foreign key \\`reel_likes_reel_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "reel_likes", "type": "table", "schema": "public", "fkey_name": "reel_likes_reel_id_fkey", "fkey_columns": [3]}, "cache_key": "unindexed_foreign_keys_public_reel_likes_reel_likes_reel_id_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.reel_views\\` has a foreign key \\`reel_views_reel_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "reel_views", "type": "table", "schema": "public", "fkey_name": "reel_views_reel_id_fkey", "fkey_columns": [3]}, "cache_key": "unindexed_foreign_keys_public_reel_views_reel_views_reel_id_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.rooms\\` has a foreign key \\`rooms_creator_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "rooms", "type": "table", "schema": "public", "fkey_name": "rooms_creator_id_fkey", "fkey_columns": [4]}, "cache_key": "unindexed_foreign_keys_public_rooms_rooms_creator_id_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.stories\\` has a foreign key \\`stories_original_post_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "stories", "type": "table", "schema": "public", "fkey_name": "stories_original_post_id_fkey", "fkey_columns": [8]}, "cache_key": "unindexed_foreign_keys_public_stories_stories_original_post_id_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.stories\\` has a foreign key \\`stories_original_reel_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "stories", "type": "table", "schema": "public", "fkey_name": "stories_original_reel_id_fkey", "fkey_columns": [9]}, "cache_key": "unindexed_foreign_keys_public_stories_stories_original_reel_id_fkey"}, {"name": "unindexed_foreign_keys", "title": "Unindexed foreign keys", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Identifies foreign key constraints without a covering index, which can impact database performance.", "detail": "Table \\`public.stories\\` has a foreign key \\`stories_shared_from_user_id_fkey\\` without a covering index. This can lead to suboptimal query performance.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0001_unindexed_foreign_keys", "metadata": {"name": "stories", "type": "table", "schema": "public", "fkey_name": "stories_shared_from_user_id_fkey", "fkey_columns": [10]}, "cache_key": "unindexed_foreign_keys_public_stories_stories_shared_from_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_comments_post_id_fkey\\` on table \\`public.comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_comments_idx_comments_post_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_comments_user_id_fkey\\` on table \\`public.comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_comments_idx_comments_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_follows_following_id_fkey\\` on table \\`public.follows\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "follows", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_follows_idx_follows_following_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_reel_comment_likes_user_id_fkey\\` on table \\`public.reel_comment_likes\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "reel_comment_likes", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_reel_comment_likes_idx_reel_comment_likes_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_reel_comments_parent_comment_id_fkey\\` on table \\`public.reel_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "reel_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_reel_comments_idx_reel_comments_parent_comment_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_reel_comments_reel_id_fkey\\` on table \\`public.reel_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "reel_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_reel_comments_idx_reel_comments_reel_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_reel_comments_user_id_fkey\\` on table \\`public.reel_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "reel_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_reel_comments_idx_reel_comments_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_room_messages_room_id_fkey\\` on table \\`public.room_messages\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "room_messages", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_room_messages_idx_room_messages_room_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_comment_likes_user_id_fkey\\` on table \\`public.comment_likes\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "comment_likes", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_comment_likes_idx_comment_likes_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_comments_parent_comment_id_fkey\\` on table \\`public.comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_comments_idx_comments_parent_comment_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_message_reactions_user_id_fkey\\` on table \\`public.message_reactions\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "message_reactions", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_message_reactions_idx_message_reactions_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_reels_user_id_fkey\\` on table \\`public.reels\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "reels", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_reels_idx_reels_user_id_fkey"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_notifications_recipient_id\\` on table \\`public.notifications\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "notifications", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_notifications_idx_notifications_recipient_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_notifications_post_id\\` on table \\`public.notifications\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "notifications", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_notifications_idx_notifications_post_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_notifications_reel_id\\` on table \\`public.notifications\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "notifications", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_notifications_idx_notifications_reel_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_notifications_comment_id\\` on table \\`public.notifications\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "notifications", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_notifications_idx_notifications_comment_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_posts_community_id\\` on table \\`public.community_posts\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_posts", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_posts_idx_community_posts_community_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_posts_author_id\\` on table \\`public.community_posts\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_posts", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_posts_idx_community_posts_author_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_comments_post_id\\` on table \\`public.community_post_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_comments_idx_community_post_comments_post_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_comments_author_id\\` on table \\`public.community_post_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_comments_idx_community_post_comments_author_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_likes_user_id\\` on table \\`public.community_post_likes\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_likes", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_likes_idx_community_post_likes_user_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_likes_post_id\\` on table \\`public.community_post_likes\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_likes", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_likes_idx_community_post_likes_post_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_members_user_id\\` on table \\`public.community_members\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_members", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_members_idx_community_members_user_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_stories_user_id\\` on table \\`public.stories\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "stories", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_stories_idx_stories_user_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_communities_creator_id\\` on table \\`public.communities\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "communities", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_communities_idx_communities_creator_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_communities_category_id\\` on table \\`public.communities\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "communities", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_communities_idx_communities_category_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_shares_post_id\\` on table \\`public.community_post_shares\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_shares", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_shares_idx_community_post_shares_post_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_shares_user_id\\` on table \\`public.community_post_shares\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_shares", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_shares_idx_community_post_shares_user_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_shares_shared_to_community_id\\` on table \\`public.community_post_shares\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_shares", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_shares_idx_community_post_shares_shared_to_community_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_comment_likes_user_id\\` on table \\`public.community_post_comment_likes\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_comment_likes", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_comment_likes_idx_community_post_comment_likes_user_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_comment_likes_comment_id\\` on table \\`public.community_post_comment_likes\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_comment_likes", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_comment_likes_idx_community_post_comment_likes_comment_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_community_post_comments_parent_comment_id\\` on table \\`public.community_post_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "community_post_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_community_post_comments_idx_community_post_comments_parent_comment_id"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_messages_receiver_id_is_read\\` on table \\`public.messages\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "messages", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_messages_idx_messages_receiver_id_is_read"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_messages_created_at\\` on table \\`public.messages\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "messages", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_messages_idx_messages_created_at"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_messages_conversation\\` on table \\`public.messages\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "messages", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_messages_idx_messages_conversation"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_profiles_username\\` on table \\`public.profiles\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "profiles", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_profiles_idx_profiles_username"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_comments_pinned_by\\` on table \\`public.comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_comments_idx_comments_pinned_by"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_reel_comments_pinned_by\\` on table \\`public.reel_comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "reel_comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_reel_comments_idx_reel_comments_pinned_by"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_notifications_recipient_is_read\\` on table \\`public.notifications\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "notifications", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_notifications_idx_notifications_recipient_is_read"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_comments_post_created\\` on table \\`public.comments\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "comments", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_comments_idx_comments_post_created"}, {"name": "unused_index", "title": "Unused Index", "level": "INFO", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if an index has never been used and may be a candidate for removal.", "detail": "Index \\`idx_follows_following_follower\\` on table \\`public.follows\\` has not been used", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0005_unused_index", "metadata": {"name": "follows", "type": "table", "schema": "public"}, "cache_key": "unused_index_public_follows_idx_follows_following_follower"}]