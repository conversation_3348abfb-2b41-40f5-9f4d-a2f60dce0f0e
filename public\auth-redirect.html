<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to Klicktape App...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            text-align: center;
        }
        .container {
            max-width: 400px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .manual-link {
            display: inline-block;
            margin-top: 1rem;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .manual-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        #error-message {
            display: none;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Redirecting to Klicktape App...</h2>
        <p class="message">Please wait while we redirect you to the app.</p>
        <div id="error-message">
            <p>If the app didn't open automatically:</p>
            <a href="#" id="manual-link" class="manual-link">Open Klicktape App</a>
        </div>
    </div>

    <script>
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            const hash = window.location.hash.substring(1);
            const hashParams = new URLSearchParams(hash);
            
            // Combine URL params and hash params
            const allParams = {};
            for (const [key, value] of params.entries()) {
                allParams[key] = value;
            }
            for (const [key, value] of hashParams.entries()) {
                allParams[key] = value;
            }
            
            return allParams;
        }

        window.onload = function() {
            console.log('Auth redirect page loaded');
            console.log('Current URL:', window.location.href);
            
            const params = getUrlParams();
            console.log('URL parameters:', params);

            // Check if we have an access token
            if (params.access_token) {
                const tokenParam = encodeURIComponent(params.access_token);
                const refreshParam = encodeURIComponent(params.refresh_token || '');
                
                // Create the deep link URL
                const deepLinkUrl = `exp://192.168.31.241:8081/--/reset-password?access_token=${tokenParam}&refresh_token=${refreshParam}`;
                
                console.log('Redirecting to app:', deepLinkUrl);
                
                // Try to open the app
                window.location.href = deepLinkUrl;
                
                // Set up manual link as fallback
                document.getElementById('manual-link').href = deepLinkUrl;
                
                // Show manual option after 3 seconds if auto-redirect fails
                setTimeout(() => {
                    document.getElementById('error-message').style.display = 'block';
                    document.querySelector('.spinner').style.display = 'none';
                    document.querySelector('.message').textContent = 'If the app didn\'t open automatically:';
                }, 3000);
            } else {
                // No token found
                console.error('No access token found in URL');
                document.getElementById('error-message').style.display = 'block';
                document.querySelector('.spinner').style.display = 'none';
                document.querySelector('.message').textContent = 'Invalid password reset link.';
                document.getElementById('error-message').innerHTML = `
                    <div style="color: #ff6b6b;">
                        No access token found. Please request a new password reset.
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
